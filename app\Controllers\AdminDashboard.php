<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Models\UsersModel;
use App\Models\FarmerInformationModel;
use App\Models\CropsFarmBlockModel;
use App\Models\LivestockFarmBlockModel;
use App\Models\PermissionsSetsModel;
use App\Models\PermissionsUserDistrictsModel;
use App\Models\AdxDistrictModel;

class AdminDashboard extends BaseController
{
    protected $usersModel;
    protected $farmerModel;
    protected $cropBlockModel;
    protected $livestockBlockModel;
    protected $permissionsSetsModel;
    protected $permissionsUserDistrictsModel;
    protected $districtModel;

    public function __construct()
    {
        $this->usersModel = new UsersModel();
        $this->farmerModel = new FarmerInformationModel();
        $this->cropBlockModel = new CropsFarmBlockModel();
        $this->livestockBlockModel = new LivestockFarmBlockModel();
        $this->permissionsSetsModel = new PermissionsSetsModel();
        $this->permissionsUserDistrictsModel = new PermissionsUserDistrictsModel();
        $this->districtModel = new AdxDistrictModel();
        helper(['form', 'url', 'info']);
    }

    /**
     * Admin Dashboard - Using hardcoded dummy data
     */
    public function index()
    {
        // Hardcoded dummy data for admin dashboard
        $stats = [
            'total_users' => 45,
            'active_users' => 38,
            'admin_users' => 3,
            'supervisor_users' => 5,
            'field_users' => 37,
            'total_farmers' => 1250,
            'active_farmers' => 1180,
            'total_crop_blocks' => 890,
            'total_livestock_blocks' => 340
        ];

        // Dummy recent users data following users table schema
        $recentUsers = [
            [
                'id' => 15,
                'sys_no' => 202501,
                'org_id' => 1,
                'name' => 'John Smith',
                'role' => 'user',
                'is_admin' => 0,
                'is_supervisor' => 0,
                'position' => 'Field Officer',
                'id_photo' => 'public/uploads/users/john_smith.jpg',
                'phone' => '+675 7123 4567',
                'email' => '<EMAIL>',
                'status' => 1,
                'status_at' => date('Y-m-d H:i:s', strtotime('-2 days')),
                'status_by' => 1,
                'status_remarks' => 'Active user',
                'created_by' => 'admin',
                'updated_by' => null,
                'created_at' => date('Y-m-d H:i:s', strtotime('-2 days')),
                'updated_at' => date('Y-m-d H:i:s', strtotime('-2 days'))
            ],
            [
                'id' => 16,
                'sys_no' => 202502,
                'org_id' => 1,
                'name' => 'Sarah Johnson',
                'role' => 'user',
                'is_admin' => 0,
                'is_supervisor' => 1,
                'position' => 'Regional Supervisor',
                'id_photo' => 'public/uploads/users/sarah_johnson.jpg',
                'phone' => '+675 7234 5678',
                'email' => '<EMAIL>',
                'status' => 1,
                'status_at' => date('Y-m-d H:i:s', strtotime('-5 days')),
                'status_by' => 1,
                'status_remarks' => 'Active supervisor',
                'created_by' => 'admin',
                'updated_by' => null,
                'created_at' => date('Y-m-d H:i:s', strtotime('-5 days')),
                'updated_at' => date('Y-m-d H:i:s', strtotime('-5 days'))
            ],
            [
                'id' => 17,
                'sys_no' => 202503,
                'org_id' => 1,
                'name' => 'Michael Brown',
                'role' => 'user',
                'is_admin' => 1,
                'is_supervisor' => 0,
                'position' => 'System Administrator',
                'id_photo' => 'public/uploads/users/michael_brown.jpg',
                'phone' => '+675 7345 6789',
                'email' => '<EMAIL>',
                'status' => 1,
                'status_at' => date('Y-m-d H:i:s', strtotime('-1 week')),
                'status_by' => 1,
                'status_remarks' => 'Admin user',
                'created_by' => 'system',
                'updated_by' => null,
                'created_at' => date('Y-m-d H:i:s', strtotime('-1 week')),
                'updated_at' => date('Y-m-d H:i:s', strtotime('-1 week'))
            ],
            [
                'id' => 18,
                'sys_no' => 202504,
                'org_id' => 1,
                'name' => 'Emily Davis',
                'role' => 'user',
                'is_admin' => 0,
                'is_supervisor' => 0,
                'position' => 'Data Entry Clerk',
                'id_photo' => 'public/uploads/users/emily_davis.jpg',
                'phone' => '+675 7456 7890',
                'email' => '<EMAIL>',
                'status' => 1,
                'status_at' => date('Y-m-d H:i:s', strtotime('-10 days')),
                'status_by' => 1,
                'status_remarks' => 'Active user',
                'created_by' => 'admin',
                'updated_by' => null,
                'created_at' => date('Y-m-d H:i:s', strtotime('-10 days')),
                'updated_at' => date('Y-m-d H:i:s', strtotime('-10 days'))
            ],
            [
                'id' => 19,
                'sys_no' => 202505,
                'org_id' => 1,
                'name' => 'David Wilson',
                'role' => 'guest',
                'is_admin' => 0,
                'is_supervisor' => 0,
                'position' => 'Temporary Access',
                'id_photo' => 'public/uploads/users/default.jpg',
                'phone' => '+675 7567 8901',
                'email' => '<EMAIL>',
                'status' => 0,
                'status_at' => date('Y-m-d H:i:s', strtotime('-2 weeks')),
                'status_by' => 1,
                'status_remarks' => 'Inactive guest account',
                'created_by' => 'admin',
                'updated_by' => 'admin',
                'created_at' => date('Y-m-d H:i:s', strtotime('-2 weeks')),
                'updated_at' => date('Y-m-d H:i:s', strtotime('-1 week'))
            ]
        ];

        // Dummy user distribution by role
        $usersByRole = [
            ['role' => 'user', 'count' => 37],
            ['role' => 'guest', 'count' => 8]
        ];

        // Dummy farmers data following farmer_information schema
        $recentFarmers = [
            [
                'id' => 1,
                'org_id' => 1,
                'farmer_code' => 'F001',
                'given_name' => 'Peter',
                'surname' => 'Kila',
                'date_of_birth' => '1985-03-15',
                'gender' => 'Male',
                'village' => 'Waigani',
                'ward_id' => 1,
                'llg_id' => 1,
                'district_id' => 1,
                'province_id' => 1,
                'country_id' => 1,
                'phone' => '+675 7111 2222',
                'email' => '<EMAIL>',
                'address' => 'Section 14, Waigani, NCD',
                'marital_status' => 'Married',
                'highest_education_id' => 3,
                'course_taken' => 'Agriculture Certificate',
                'id_photo' => 'public/uploads/farmers/peter_kila.jpg',
                'status' => 'active',
                'created_at' => date('Y-m-d H:i:s', strtotime('-1 month')),
                'created_by' => 15,
                'updated_at' => date('Y-m-d H:i:s', strtotime('-1 month')),
                'updated_by' => null
            ],
            [
                'id' => 2,
                'org_id' => 1,
                'farmer_code' => 'F002',
                'given_name' => 'Mary',
                'surname' => 'Temu',
                'date_of_birth' => '1978-07-22',
                'gender' => 'Female',
                'village' => 'Gerehu',
                'ward_id' => 2,
                'llg_id' => 1,
                'district_id' => 1,
                'province_id' => 1,
                'country_id' => 1,
                'phone' => '+675 7222 3333',
                'email' => '<EMAIL>',
                'address' => 'Stage 6, Gerehu, NCD',
                'marital_status' => 'Single',
                'highest_education_id' => 2,
                'course_taken' => null,
                'id_photo' => 'public/uploads/farmers/mary_temu.jpg',
                'status' => 'active',
                'created_at' => date('Y-m-d H:i:s', strtotime('-3 weeks')),
                'created_by' => 16,
                'updated_at' => date('Y-m-d H:i:s', strtotime('-3 weeks')),
                'updated_by' => null
            ]
        ];

        // Dummy district coverage data
        $districtCoverage = [
            ['district_name' => 'National Capital District', 'user_count' => 12],
            ['district_name' => 'Central Province', 'user_count' => 8],
            ['district_name' => 'Western Province', 'user_count' => 10],
            ['district_name' => 'Gulf Province', 'user_count' => 6],
            ['district_name' => 'Milne Bay Province', 'user_count' => 9]
        ];

        // Dummy crop farm blocks data following crops_farm_blocks schema
        $recentCropBlocks = [
            [
                'id' => 1,
                'exercise_id' => 1,
                'farmer_id' => 1,
                'crop_id' => 1,
                'block_code' => 'F8010001',
                'org_id' => 1,
                'country_id' => 1,
                'province_id' => 1,
                'district_id' => 1,
                'llg_id' => 1,
                'ward_id' => 1,
                'village' => 'Waigani',
                'block_site' => 'Behind Community Center',
                'lon' => '147.1234',
                'lat' => '-9.4567',
                'remarks' => 'Good soil condition',
                'status' => 'active',
                'created_by' => 15,
                'created_at' => date('Y-m-d H:i:s', strtotime('-2 weeks')),
                'updated_by' => null,
                'updated_at' => null,
                'deleted_at' => null,
                'deleted_by' => null
            ],
            [
                'id' => 2,
                'exercise_id' => 1,
                'farmer_id' => 2,
                'crop_id' => 2,
                'block_code' => 'F8010002',
                'org_id' => 1,
                'country_id' => 1,
                'province_id' => 1,
                'district_id' => 1,
                'llg_id' => 1,
                'ward_id' => 2,
                'village' => 'Gerehu',
                'block_site' => 'Near Main Road',
                'lon' => '147.2345',
                'lat' => '-9.5678',
                'remarks' => 'Needs irrigation',
                'status' => 'active',
                'created_by' => 16,
                'created_at' => date('Y-m-d H:i:s', strtotime('-10 days')),
                'updated_by' => null,
                'updated_at' => null,
                'deleted_at' => null,
                'deleted_by' => null
            ]
        ];

        // Dummy livestock farm blocks data following livestock_farm_blocks schema
        $recentLivestockBlocks = [
            [
                'id' => 1,
                'exercise_id' => 1,
                'farmer_id' => 1,
                'block_code' => 'L8010001',
                'org_id' => 1,
                'country_id' => 1,
                'province_id' => 1,
                'district_id' => 1,
                'llg_id' => 1,
                'ward_id' => 1,
                'village' => 'Waigani',
                'block_site' => 'Backyard Pen',
                'lon' => '147.1234',
                'lat' => '-9.4567',
                'remarks' => 'Small scale poultry',
                'status' => 'active',
                'created_by' => 15,
                'created_at' => date('Y-m-d H:i:s', strtotime('-1 week')),
                'updated_by' => null,
                'updated_at' => null,
                'deleted_at' => null,
                'deleted_by' => null
            ]
        ];

        // Dummy top permission users
        $topPermissionUsers = [
            ['name' => 'Admin User', 'role' => 'user', 'permission_count' => 25],
            ['name' => 'Supervisor One', 'role' => 'user', 'permission_count' => 18],
            ['name' => 'Field Manager', 'role' => 'user', 'permission_count' => 15],
            ['name' => 'Data Analyst', 'role' => 'user', 'permission_count' => 12],
            ['name' => 'Regional Lead', 'role' => 'user', 'permission_count' => 10]
        ];

        // Dummy monthly registration trend (last 6 months)
        $monthlyRegistrations = [
            ['month' => date('M Y', strtotime('-5 months')), 'count' => 3],
            ['month' => date('M Y', strtotime('-4 months')), 'count' => 7],
            ['month' => date('M Y', strtotime('-3 months')), 'count' => 5],
            ['month' => date('M Y', strtotime('-2 months')), 'count' => 12],
            ['month' => date('M Y', strtotime('-1 month')), 'count' => 8],
            ['month' => date('M Y'), 'count' => 10]
        ];

        $data = [
            'title' => 'Admin Dashboard',
            'page_header' => 'Admin Dashboard',
            'page_desc' => 'Organization Management Overview',
            'menu' => 'admin-dashboard',
            'stats' => $stats,
            'recentUsers' => $recentUsers,
            'usersByRole' => $usersByRole,
            'districtCoverage' => $districtCoverage,
            'topPermissionUsers' => $topPermissionUsers,
            'monthlyRegistrations' => $monthlyRegistrations,
            'recentFarmers' => $recentFarmers,
            'recentCropBlocks' => $recentCropBlocks,
            'recentLivestockBlocks' => $recentLivestockBlocks
        ];

        return view('admin_dashboard/admin_dashboard_index', $data);
    }


}
